import { useState, useEffect } from "react";
import { FiSearch, FiChevronDown } from "react-icons/fi";
import useProjectsStore from "../../store/destinationStore";
import useResaleStore from "../../store/resaleStore";
import { useLocation, useNavigate } from "react-router-dom";
import useDeveloperStore from "../../store/developerStore";

const heroImg = "/images/heroImage/destination.webp";

// Predefined price options for resale
const priceOptions = [
  { label: "Any", value: "any" },
  { label: "0 - 500,000", value: "500000" },
  { label: "500,001 - 1,000,000", value: "1000000" },
  { label: "1,000,001 - 2,000,000", value: "2000000" },
  { label: "2,000,001 - 3,000,000", value: "3000000" },
  { label: "3,000,001+", value: "100000000" },
];

export default function Hero() {
  const {
    setFilters: setProjectFilters,
    fetchProjects,
    filters: projectFilters,
  } = useProjectsStore();
  const {
    setFilters: setResaleFilters,
    fetchResales,
    filters: resaleFilters,
  } = useResaleStore();
  const locationConfig = useLocation();
  const navigate = useNavigate();
  const params = new URLSearchParams(locationConfig.search);

  const [projectType, setProjectType] = useState("resale");
  const [location, setLocation] = useState("all");
  const [developer, setDeveloper] = useState("all");
  const [maxPrice, setMaxPrice] = useState("any");
  const [area, setArea] = useState("all");
  const [destinations, setDestinations] = useState([]);
  const [areas, setAreas] = useState([]);

  const { developers, fetchDevelopers } = useDeveloperStore();

  useEffect(() => {
    fetchDevelopers();
  }, [fetchDevelopers]);

  // Fetch destinations
  useEffect(() => {
    const fetchDestinations = async () => {
      try {
        const res = await fetch(
          `${
            import.meta.env.VITE_API_BASE_URL
          }/api/destinations?pageSize=100&pageNumber=1`
        );
        const data = await res.json();
        setDestinations(data.destinations || []);
      } catch (err) {
        console.error("Failed to fetch destinations:", err);
        setDestinations([]);
      }
    };
    fetchDestinations();
  }, []);

  useEffect(() => {
    const fetchAreas = async () => {
      try {
        const res = await fetch(
          `${import.meta.env.VITE_API_BASE_URL}/api/areas`
        );
        const data = await res.json();
        setAreas(data.areas || []);
      } catch (err) {
        console.error("Failed to fetch destinations:", err);
        setAreas([]);
      }
    };
    fetchAreas();
  }, []);

  // Sync Hero component state when store filters change
  useEffect(() => {
    if (projectType === "off-plan" && projectFilters) {
      setLocation(projectFilters.destination || "all");
      setDeveloper(projectFilters.developer || "all");
    } else if (projectType === "resale" && resaleFilters) {
      setLocation(resaleFilters.destination || "all");
      setMaxPrice(resaleFilters.maxPrice || "");
      setArea(resaleFilters.area || "all");
    }
  }, [projectFilters, resaleFilters, projectType]);

  // Sync filter state and search on URL query change
  useEffect(() => {
    const destFromUrl = params.get("destination") || "all";
    const devFromUrl = params.get("developer") || "all";
    const typeFromUrl = params.get("type") || "resale";
    const maxFromUrl = params.get("maxPrice") || "";
    const areaFromUrl = params.get("area") || "all"; // ✅ add this
    setLocation(destFromUrl);
    setDeveloper(devFromUrl);
    setProjectType(typeFromUrl);
    setMaxPrice(maxFromUrl);
    setArea(areaFromUrl); // ✅ add this

    if (typeFromUrl === "off-plan") {
      const filters = {
        destination: destFromUrl === "all" ? null : destFromUrl,
        developer: devFromUrl === "all" ? null : devFromUrl,
      };
      setProjectFilters(filters);
      fetchProjects(filters);
    } else {
      const filters = {
        destination: destFromUrl === "all" ? null : destFromUrl,
        maxPrice: maxPrice === "any" ? undefined : Number(maxPrice),
        area: areaFromUrl === "all" ? undefined : areaFromUrl, // ✅ add this
      };
      setMaxPrice(
        resaleFilters.maxPrice ? resaleFilters.maxPrice.toString() : "any"
      );
      setResaleFilters(filters);

      fetchResales(filters);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [locationConfig.search]);

  const handleSearch = () => {
    const query = new URLSearchParams();
    query.set("type", projectType);
    if (location !== "all") query.set("destination", location);
    if (developer !== "all") query.set("developer", developer);
    if (maxPrice) query.set("maxPrice", maxPrice);
    if (area !== "all") query.set("area", area);

    navigate(`/destinations?${query.toString()}`);

    if (projectType === "off-plan") {
      const newFilters = {
        destination: location === "all" ? null : location,
        developer: developer === "all" ? null : developer,
      };
      setProjectFilters(newFilters);
      fetchProjects(newFilters);
    } else {
      const newFilters = {
        destination: location === "all" ? null : location,
        maxPrice: maxPrice === "any" ? undefined : Number(maxPrice),
        area: area === "all" ? undefined : area,
      };
      setResaleFilters(newFilters);
      fetchResales(newFilters);
    }
  };

  return (
    <section className="w-full text-center h-screen flex flex-col pt-30">
      {/* Headings */}
      <div className="max-w-[1200px] mx-auto pt-12 px-4 flex-shrink-0">
        <h1 className="font-playfair text-4xl sm:text-5xl md:text-6xl leading-[1.1] font-medium tracking-[-0.04em]">
          Where Dubai Investment
          <br />
          <span className="italic text-[#D1A954]">Becomes Reality</span>
        </h1>
        <p className="font-outfit font-light text-base sm:text-lg md:text-xl mt-6 max-w-4xl mx-auto">
          Strategically located <span className="font-semibold">projects</span>{" "}
          offering <span className="font-semibold">strong returns</span>,
          long-term growth.
        </p>
      </div>

      {/* Hero Image + Filter Bar */}
      <div className="relative mt-12 flex-1 min-h-0">
        <img
          src={heroImg}
          alt="Dubai Skyline"
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 flex items-center justify-center ">
          {/* Desktop Filter Bar */}
          <div className="hidden md:flex bg-[#FCFCFCAB] backdrop-blur-sm shadow-md rounded-full px-4 py-2 items-center justify-between h-20 w-10/12 md:h-24">
            {/* Project Type */}
            <div className="flex flex-col flex-1 relative text-start pl-10">
              <label className="font-outfit font-normal md:text-lg text-base text-[#1E1E1E] mb-1">
                Project Type
              </label>
              <div className="relative">
                <select
                  className="appearance-none font-outfit font-normal text-base text-[#5E5E5E] bg-transparent w-full pr-6"
                  value={projectType}
                  onChange={(e) => setProjectType(e.target.value)}
                >
                  <option value="off-plan">Off Plan</option>
                  <option value="resale">Resale</option>
                </select>
                <FiChevronDown
                  className="absolute right-0 top-1/2 -translate-y-1/2 text-[#5E5E5E]"
                  size={16}
                />
              </div>
            </div>

            <div className="border-l h-3/4 mx-4" />

            {/* Location */}
            <div className="flex flex-col flex-1 relative text-start pl-10">
              <label className="font-outfit font-normal text-lg text-[#1E1E1E] mb-1">
                Location
              </label>
              <div className="relative">
                <select
                  className="appearance-none font-outfit font-normal text-base text-[#5E5E5E] bg-transparent w-full pr-6"
                  value={location}
                  onChange={(e) => setLocation(e.target.value)}
                >
                  <option value="all">All</option>
                  {destinations.map((dest) => (
                    <option key={dest._id} value={dest._id}>
                      {dest.name}
                    </option>
                  ))}
                </select>
                <FiChevronDown
                  className="absolute right-0 top-1/2 -translate-y-1/2 text-[#5E5E5E]"
                  size={16}
                />
              </div>
            </div>

            {projectType === "off-plan" && (
              <>
                <div className="border-l h-3/4 mx-4" />
                {/* Developer */}
                <div className="flex flex-col flex-1 relative text-start pl-10">
                  <label className="font-outfit font-normal text-lg text-[#1E1E1E] mb-1">
                    Developer
                  </label>
                  <div className="relative">
                    <select
                      className="appearance-none font-outfit font-normal text-base text-[#5E5E5E] bg-transparent w-full pr-6"
                      value={developer}
                      onChange={(e) => setDeveloper(e.target.value)}
                    >
                      <option value="all">All</option>
                      {developers.map((dev) => (
                        <option key={dev._id} value={dev._id}>
                          {dev.name}
                        </option>
                      ))}
                    </select>
                    <FiChevronDown
                      className="absolute right-0 top-1/2 -translate-y-1/2 text-[#5E5E5E]"
                      size={16}
                    />
                  </div>
                </div>
              </>
            )}

            {projectType === "resale" && (
              <>
                <div className="border-l h-3/4 mx-4" />
                {/* Max Price */}
                <div className="flex flex-col flex-1 relative text-start pl-10">
                  <label className="font-outfit font-normal text-lg text-[#1E1E1E] mb-1">
                    Max Price
                  </label>
                  <div className="relative">
                    <select
                      className="appearance-none font-outfit font-normal text-base text-[#5E5E5E] bg-transparent w-full pr-6"
                      value={maxPrice}
                      onChange={(e) => setMaxPrice(e.target.value)}
                    >
                      {priceOptions.map((opt) => (
                        <option key={opt.value} value={opt.value}>
                          {opt.label}
                        </option>
                      ))}
                    </select>
                    <FiChevronDown
                      className="absolute right-0 top-1/2 -translate-y-1/2 text-[#5E5E5E]"
                      size={16}
                    />
                  </div>
                </div>
                <div className="border-l h-3/4 mx-4" />

                <div className="flex flex-col flex-1 relative text-start pl-10">
                  <label className="font-outfit font-normal text-lg text-[#1E1E1E] mb-1">
                    Areas
                  </label>
                  <div className="relative">
                    <select
                      className="appearance-none font-outfit font-normal text-base text-[#5E5E5E] bg-transparent w-full pr-6"
                      value={area}
                      onChange={(e) => setArea(e.target.value)}
                    >
                      <option value="all">All Areas</option>
                      {areas.map((a) => (
                        <option key={a._id} value={a._id}>
                          {a.name}
                        </option>
                      ))}
                    </select>
                    <FiChevronDown
                      className="absolute right-0 top-1/2 -translate-y-1/2 text-[#5E5E5E]"
                      size={16}
                    />
                  </div>
                </div>
              </>
            )}

            {/* Search Button */}
            <button
              onClick={handleSearch}
              className="bg-[#D1A954] hover:bg-[#b8923c] text-white font-outfit font-semibold text-base px-6 py-4 rounded-full flex items-center gap-2"
            >
              <FiSearch size={20} />
              Search
            </button>
          </div>


          {/* mobile filter  */}
          <div className="md:hidden  bg-white/90 backdrop-blur-md shadow-lg rounded-lg p-2 flex flex-col gap-2 w-8/12 max-w-[280px] mx-auto">
            {/* Project Type */}
            <div className="flex flex-col gap-1 relative">
              <label className="font-outfit font-medium text-sm text-gray-700">
                Project Type
              </label>
              <select
                className="appearance-none font-outfit font-normal text-gray-600 text-sm bg-gray-100 rounded-lg px-3 py-2 pr-8 focus:outline-none focus:ring-2 focus:ring-[#D1A954] transition"
                value={projectType}
                onChange={(e) => setProjectType(e.target.value)}
              >
                <option value="off-plan">Off Plan</option>
                <option value="resale">Resale</option>
              </select>
              <FiChevronDown className="pointer-events-none absolute right-2 top-1/2 -translate-y-1/2 text-gray-400" size={14} />
            </div>

            {/* Location */}
            <div className="flex flex-col gap-1 relative">
              <label className="font-outfit font-medium text-sm text-gray-700">
                Location
              </label>
              <select
                className="appearance-none font-outfit font-normal text-gray-600 text-sm bg-gray-100 rounded-lg px-3 py-2 pr-8 focus:outline-none focus:ring-2 focus:ring-[#D1A954] transition"
                value={location}
                onChange={(e) => setLocation(e.target.value)}
              >
                <option value="all">All</option>
                {destinations.map((dest) => (
                  <option key={dest._id} value={dest._id}>
                    {dest.name}
                  </option>
                ))}
              </select>
              <FiChevronDown className="pointer-events-none absolute right-2 top-1/2 -translate-y-1/2 text-gray-400" size={14} />
            </div>

            {/* Developer (off-plan) */}
            {projectType === "off-plan" && (
              <div className="flex flex-col gap-1 relative">
                <label className="font-outfit font-medium text-sm text-gray-700">
                  Developer
                </label>
                <select
                  className="appearance-none font-outfit font-normal text-gray-600 text-sm bg-gray-100 rounded-lg px-3 py-2 pr-8 focus:outline-none focus:ring-2 focus:ring-[#D1A954] transition"
                  value={developer}
                  onChange={(e) => setDeveloper(e.target.value)}
                >
                  <option value="all">All</option>
                  {developers.map((dev) => (
                    <option key={dev._id} value={dev._id}>
                      {dev.name}
                    </option>
                  ))}
                </select>
                <FiChevronDown className="pointer-events-none absolute right-2 top-1/2 -translate-y-1/2 text-gray-400" size={14} />
              </div>
            )}

            {/* Max Price (resale) */}
            {projectType === "resale" && (
              <div className="flex flex-col gap-3">
                {/* Max Price */}
                <div className="flex flex-col gap-1 relative">
                  <label className="font-outfit font-medium text-sm text-gray-700">
                    Max Price
                  </label>
                  <select
                    className="appearance-none font-outfit font-normal text-gray-600 text-sm bg-gray-100 rounded-lg px-3 py-2 pr-8 focus:outline-none focus:ring-2 focus:ring-[#D1A954] transition"
                    value={maxPrice}
                    onChange={(e) => setMaxPrice(e.target.value)}
                  >
                    {priceOptions.map((opt) => (
                      <option key={opt.value} value={opt.value}>
                        {opt.label}
                      </option>
                    ))}
                  </select>
                  <FiChevronDown className="pointer-events-none absolute right-2 top-1/2 -translate-y-1/2 text-gray-400" size={14} />
                </div>

                {/* Areas */}
                <div className="flex flex-col gap-1 relative">
                  <label className="font-outfit font-medium text-sm text-gray-700">
                    Areas
                  </label>
                  <select
                    className="appearance-none font-outfit font-normal text-gray-600 text-sm bg-gray-100 rounded-lg px-3 py-2 pr-8 focus:outline-none focus:ring-2 focus:ring-[#D1A954] transition"
                    value={area}
                    onChange={(e) => setArea(e.target.value)}
                  >
                    <option value="all">All Areas</option>
                    {areas.map((a) => (
                      <option key={a._id} value={a._id}>
                        {a.name}
                      </option>
                    ))}
                  </select>
                  <FiChevronDown className="pointer-events-none absolute right-2 top-1/2 -translate-y-1/2 text-gray-400" size={14} />
                </div>
              </div>
            )}

            {/* Search Button */}
            <button
              onClick={handleSearch}
              className="bg-[#D1A954] hover:bg-[#b38f43] transition text-white rounded-full px-4 py-2.5 flex items-center justify-center gap-2 font-outfit font-medium text-sm shadow-md"
            >
              <FiSearch size={16} />
              Search
            </button>
          </div>
        </div>
      </div>
    </section>
  );
}
